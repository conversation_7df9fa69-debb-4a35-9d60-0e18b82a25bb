<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
    @click.self="cancel"
  >
    <div
      class="bg-secondary rounded-lg shadow-lg p-1 max-w-6xl w-full animate-popup"
    >
      <div class="flex justify-between">
        <div></div>
        <div class="text-lg font-bold mb-2 text-center">Xuất hóa đơn</div>
        <div class="text-red-500 cursor-pointer" @click="cancel">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-5"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
            />
          </svg>
        </div>
      </div>
      <!--  -->

      <div class="mb-4">
        <div
          v-if="
            order?.order.customAttribute.exportVatInvoiceStatus !==
            'INVOICE_PUBLISHED'
          "
          class="grid grid-cols-12"
        >
          <!-- cột 1  -->

          <div
            class="md:col-span-5 col-span-12 px-2 rounded space-y-4 max-h-[75vh] overflow-y-auto"
          >
            <div class="p-2 px-3 rounded bg-white">
              <div class="text-primary font-bold text-md pb-2">
                Thông tin xuất hóa đơn
              </div>
              <TabChangeOrder
                :tabs="tabs"
                @toogleTab="handleSetTab"
                class="mb-3 mt-2"
              ></TabChangeOrder>

              <div class="grid grid-cols-2 gap-x-4 gap-y-3 text-sm">
                <div
                  v-if="dataVatCustomer?.length"
                  class="col-span-2 flex items-center"
                >
                  <span class="w-40 font-medium">Chọn nhanh</span>
                  <select
                    id="warehouse-select"
                    class="block w-full outline-none bg-secondary px-2 py-1 rounded cursor-pointer border"
                    v-model="selectTaxCode"
                    @change="handleChangeTaxCode"
                  >
                    <option disabled value="">Chọn nhanh mã số thuế</option>
                    <option
                      v-for="tax in dataVatCustomer"
                      :value="tax"
                      :key="tax?.id"
                    >
                      {{ tax?.company }}
                    </option>
                  </select>
                </div>
                <!-- Tên người mua -->
                <div class="col-span-2 flex items-center">
                  <span class="w-40 font-medium">Người mua hàng:</span>
                  <input
                    type="text"
                    placeholder="Nhập họ tên người mua"
                    class="flex-1 border-b px-2 outline-none border-dashed"
                    v-model="buyerName"
                  />
                </div>

                <!-- Mã số thuế người mua -->
                <div
                  v-if="selected === 'COMPANY'"
                  class="col-span-2 flex items-center"
                >
                  <span class="w-40 font-medium">Mã số thuế :</span>
                  <input
                    type="text"
                    placeholder="Nhập mã số thuế"
                    class="flex-1 border-b px-2 outline-none border-dashed"
                    v-model="buyerTaxCode"
                  />
                </div>

                <!-- Tên công ty -->
                <div v-if="selected === 'COMPANY'" class="flex col-span-2">
                  <span class="w-40 font-medium">Tên đơn vị:</span>
                  <input
                    type="text"
                    placeholder="Nhập tên đơn vị"
                    class="flex-1 border-b border-dashed rounded px-2 outline-none"
                    v-model="buyerCompany"
                  />
                </div>

                <!-- Địa chỉ -->
                <div class="flex col-span-2">
                  <span class="w-40 font-medium">Địa chỉ:</span>
                  <input
                    type="email"
                    placeholder="Nhập địa chỉ"
                    class="flex-1 border-b border-dashed px-2 outline-none"
                    v-model="buyerAddress"
                  />
                </div>
                <!-- Gửi mail -->
                <div class="flex items-center py-2 col-span-2">
                  <span class="w-40 font-medium">Gửi mail:</span>
                  <label
                    class="relative inline-flex items-center cursor-pointer"
                  >
                    <input
                      type="checkbox"
                      class="sr-only peer"
                      v-model="isSendMail"
                    />
                    <div
                      class="w-11 h-6 bg-gray-300 rounded-full peer peer-checked:bg-primary transition-colors duration-300 ease-in-out"
                    ></div>
                    <div
                      class="absolute left-0.5 top-0.5 bg-white w-5 h-5 rounded-full transition-transform duration-300 ease-in-out peer-checked:translate-x-full"
                    ></div>
                  </label>
                </div>
                <!-- Tên người mua -->
                <div class="flex items-center col-span-2" v-if="isSendMail">
                  <span class="w-40 font-medium">Tên người nhận:</span>
                  <input
                    type="text"
                    placeholder="Nhập tên người nhận"
                    class="flex-1 border-b px-2 outline-none border-dashed"
                    v-model="receiverName"
                  />
                </div>

                <!-- Mã số thuế người mua -->
                <div class="flex items-center col-span-2" v-if="isSendMail">
                  <span class="w-40 font-medium">Email người nhận:</span>
                  <input
                    type="text"
                    placeholder="<EMAIL>"
                    class="flex-1 border-b px-2 outline-none border-dashed"
                    v-model="receiverEmail"
                  />
                </div>
              </div>
              <div class="flex items-center justify-end gap-2">
                <button
                  v-if="
                    order?.order.customAttribute.exportVatInvoiceStatus !==
                    'INVOICE_PUBLISHED'
                  "
                  @click="handleUpdateNewProductInvoice"
                  class="px-2 py-1 bg-white text-primary border border-primary rounded mt-6"
                >
                  Cập nhật SP
                </button>
                <button
                  v-if="
                    order?.order.customAttribute.exportVatInvoiceStatus !==
                    'INVOICE_PUBLISHED'
                  "
                  @click="handleExportInvoiceDraft"
                  class="px-2 py-1 bg-white text-primary border border-primary rounded mt-6"
                >
                  Cập nhật thông tin
                </button>
              </div>
            </div>
            <div class="p-2 bg-white rounded">
              <div class="flex items-center justify-between">
                <div class="text-primary font-bold text-md pb-2 p-2">
                  Sản phẩm
                </div>
                <span class="cursor-pointer" @click="toogleOpenEditPopup">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="size-5"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"
                    />
                  </svg>
                </span>
              </div>

              <div
                class="max-h-[28vh] overflow-y-auto"
                v-if="dataItemInvoice?.length"
              >
                <div
                  v-for="product in dataItemInvoice"
                  class="border px-2 my-2 rounded shadow-md"
                >
                  <ItemProductLineInvoice
                    :product="product"
                  ></ItemProductLineInvoice>
                </div>
              </div>
            </div>
          </div>

          <!-- cột 2 -->
          <div
            class="mx-2 md:col-span-7 col-span-12 bg-white rounded px-2 h-[75svh] overflow-y-auto"
          >
            <div class="flex items-center gap-2">
              <div class="text-primary font-bold text-md py-2 mb-2">
                {{
                  order?.order.customAttribute.exportVatInvoiceStatus ===
                  "INVOICE_PUBLISHED"
                    ? "Hóa đơn chính thức "
                    : "Hóa đơn nháp"
                }}
              </div>
            </div>
            <iframe
              v-if="dataInvoiceDraft && urlInvoice"
              :src="urlInvoice"
              width="100%"
              height="700"
              class="rounded"
              allowfullscreen
            ></iframe>
          </div>
        </div>
        <div v-else class="grid grid-cols-12 gap-2">
          <div
            class="col-span-12 bg-white p-2 rounded h-[70svh] overflow-y-auto"
          >
            <div class="flex items-center gap-2 justify-between">
              <div class="text-md py-2 mb-2 flex items-center">
                <div class="text-primary font-bold">Hóa đơn chính thức</div>
                <div class="font-bold">- {{ numberOfinvoice?.value }}</div>
              </div>
              <div
                v-if="urlInvoice"
                @click="handleCopy"
                class="text-md my-2 mb-2 flex items-center justify-center gap-2 border rounded px-2 py-1 bg-primary text-white cursor-pointer"
              >
                <span
                  ><svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="size-4"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M15.666 3.888A2.25 2.25 0 0 0 13.5 2.25h-3c-1.03 0-1.9.693-2.166 1.638m7.332 0c.055.194.084.4.084.612v0a.75.75 0 0 1-.75.75H9a.75.75 0 0 1-.75-.75v0c0-.212.03-.418.084-.612m7.332 0c.646.049 1.288.11 1.927.184 1.1.128 1.907 1.077 1.907 2.185V19.5a2.25 2.25 0 0 1-2.25 2.25H6.75A2.25 2.25 0 0 1 4.5 19.5V6.257c0-1.108.806-2.057 1.907-2.185a48.208 48.208 0 0 1 1.927-.184"
                    />
                  </svg>
                </span>
                <span class="md:block hidden">Copy link hóa đơn</span>
              </div>
            </div>
            <iframe
              v-if="urlInvoice"
              :src="urlInvoice"
              width="100%"
              height="700"
              class="rounded"
              allowfullscreen
            ></iframe>
            <div v-else class="flex items-center justify-center h-[300px]">
              <div
                @click="handleGetInvoice"
                class="bg-primary text-white p-2 rounded cursor-pointer"
              >
                Tải lại hóa đơn
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- hàng nút btn  -->
      <div class="flex justify-end space-x-4">
        <button
          v-if="
            order?.order.customAttribute.exportVatInvoiceStatus !==
            'INVOICE_PUBLISHED'
          "
          @click="toogleConfirmExportExportWareHouse"
          class="px-2 py-1 bg-primary text-white rounded"
        >
          Xuất hóa đơn chính thức
        </button>
      </div>
      <LoadingSpinner v-if="isLoading" />
      <ExportInvoiceDialog
        v-if="isConfirmExportInvoice"
        :title="'Xác nhận xuất hóa đơn chính thức'"
        :message="'Bạn có muốn xuất hóa đơn chính thức không. Vui lòng kiểm tra kĩ trước khi đồng ý.'"
        @confirm="handleExportInvoice"
        @cancel="toogleConfirmExportExportWareHouse"
      ></ExportInvoiceDialog>
      <WaringWareHouse
        v-if="isAlertWareHouse"
        :title="'Không thể xuất hóa đơn'"
        @cancel="isAlertWareHouse = false"
      ></WaringWareHouse>
      <WaringWareHouse
        v-if="isAlertRole"
        :title="'Không thể xuất hóa đơn'"
        :isAlertRole="true"
        @cancel="isAlertRole = false"
      ></WaringWareHouse>
      <EditItemProductInvoice
        v-if="isOpenEditItemInvoice"
        :order="order"
        :dataItemInvoice="dataItemInvoice"
        :dataInvoiceDraft="dataInvoiceDraft"
        @cancel="toogleOpenEditPopup"
        @confirm="handGetNewInvoiceDraft"
      ></EditItemProductInvoice>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { VatInvoiceRequestDTO } from "../../types/Invoice";

const props = defineProps(["order"]);
const emit = defineEmits(["confirm", "cancel"]);
const isVisible = ref(true);
const isAlertWareHouse = ref(false);

const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};
//
const selected = ref("");
const isSendMail = ref(false);
const dataInvoiceDraft = ref();
//
const {
  requestUnpublishVatInvoice,
  requestPublishVatInvoice,
  getInvoicesOfOrder,
  viewPublishedInvoice,
  updateInvoiceItem,
  getInvoiceItemOfInvoie,
} = useInvoice();
const { getVatInfoByOwnerPartyId, createVatInfo } = useCustomer();
const handleExportInvoiceDraft = async (data?: any) => {
  if (buyerTaxCode.value) {
    if (!buyerCompany.value) {
      useNuxtApp().$toast.warning("Vui lòng nhập tên đơn vị");
      return;
    }
  }
  if (isSendMail.value) {
    if (!receiverName.value) {
      useNuxtApp().$toast.warning("Vui lòng nhập tên người nhận");
      return;
    }
    if (!receiverEmail.value) {
      useNuxtApp().$toast.warning("Vui lòng nhập email người nhận");
      return;
    }
  }
  if (selected.value === "COMPANY") {
    if (!buyerTaxCode.value) {
      useNuxtApp().$toast.warning("Vui lòng nhập mã số thuế");
      return;
    }
  }
  const dataVatRequest = {
    sourceId: dataInvoiceDraft.value?.invoiceId
      ? dataInvoiceDraft.value?.invoiceId
      : props.order?.id,
    sourceType: dataInvoiceDraft.value?.invoiceId ? "invoice" : "order",
    signType: "MTT",
    includeBuyerTaxCode: selected.value === "COMPANY" ? true : false,
    buyerName: buyerName.value,
    buyerTaxCode: selected.value === "COMPANY" ? buyerTaxCode.value : "",
    buyerCompany: selected.value === "COMPANY" ? buyerCompany.value : "",
    buyerAddress: buyerAddress.value,
    sendMail: isSendMail.value,
    receiverName: isSendMail.value ? receiverName.value : "",
    receiverEmail: isSendMail.value ? receiverEmail.value : "",
  };
  //
  if (
    props.order?.order?.customAttribute?.exportVatInvoiceStatus ===
    "INVOICE_DRAFT"
  ) {
    await handleGetInvoiceOfOrderDraft();
    return;
  }
  //
  await handleGetUrl(dataVatRequest);
  await orderStore.updateOrder(props.order?.id);
};
const handleUpdateNewProductInvoice = async () => {
  const dataRequest = {
    sourceId: props.order?.id,
    sourceType: "order",
    signType: "MTT",
    includeBuyerTaxCode: selected.value === "COMPANY" ? true : false,
    buyerName: buyerName.value,
    buyerTaxCode: selected.value === "COMPANY" ? buyerTaxCode.value : "",
    buyerCompany: selected.value === "COMPANY" ? buyerCompany.value : "",
    buyerAddress: buyerAddress.value,
    sendMail: isSendMail.value,
    receiverName: isSendMail.value ? receiverName.value : "",
    receiverEmail: isSendMail.value ? receiverEmail.value : "",
  };
  await handleGetUrl(dataRequest);
};
const auth = useCookie("auth") as any;
const { data } = await useFetch("/data/setting.json");

// Use tab-isolated context instead of cookies
const { orgId } = useTabContext();
const { getInventory, getInventoryV2 } = useWarehouse();

const handleCheckInventory = async () => {
  const dataSettingOrg = data.value as any;
  const result = dataSettingOrg?.find(
    (org: any) => org?.storeId === orgId.value
  );
  if (result?.isExportInvoiceForProduct) {
    const data = <any>[];
    props.order?.activeOrderItemProfiles?.map(async (item: any) => {
      const sku = item.orderLineItem.variant.sku;
      const test = {
        productId: item?.orderLineItem.variant?.product?.id,
        variantId:
          item?.orderLineItem?.variant?.id ===
          item.orderLineItem.variant?.product?.id
            ? ""
            : item.orderLineItem.variant?.id,
        sku: item.orderLineItem.variant?.sku,
      };
      data.push(test);
    });
    const res = await getInventoryV2(
      props.order?.order?.customAttribute?.facilityId,
      data
    );

    if (res?.length) {
      for (const item of res) {
        if (item?.orderAble < 5) {
          return false;
        }
      }
    }
    return true;
  } else {
    return true;
  }
};
const orderStore = useOrderStore();
const isAlertRole = ref(false);
const toogleIsAlertRole = () => {
  isAlertRole.value = !isAlertRole.value;
};
const handleExportInvoice = async () => {
  //
  // check roles
  const dataSettingOrg = data.value as any;
  const arrRoles = dataSettingOrg?.find(
    (org: any) => org.storeId === orgId.value
  );
  if (arrRoles?.rolesExportInvoice?.length) {
    // const arrRoles = ["SALE_ADMIN", "SALE_MANAGER"];
    const isRoleSaleAdmin = auth.value?.user?.roles?.filter((role: any) =>
      arrRoles?.rolesExportInvoice?.includes(role)
    );
    if (!isRoleSaleAdmin?.length) {
      isAlertRole.value = true;
      toogleConfirmExportExportWareHouse();

      return;
    }
  }
  if (buyerTaxCode.value) {
    if (!buyerCompany.value) {
      useNuxtApp().$toast.warning("Vui lòng nhập tên đơn vị");
      toogleConfirmExportExportWareHouse();

      return;
    }
  }
  if (isSendMail.value) {
    if (!receiverName.value) {
      useNuxtApp().$toast.warning("Vui lòng nhập tên người nhận");
      toogleConfirmExportExportWareHouse();

      return;
    }
    if (!receiverEmail.value) {
      useNuxtApp().$toast.warning("Vui lòng nhập email người nhận");
      toogleConfirmExportExportWareHouse();

      return;
    }
  }
  if (!dataInvoiceDraft.value.invoiceId) {
    useNuxtApp().$toast.warning("Vui lòng tạo hóa đơn nháp ");
    toogleConfirmExportExportWareHouse();

    return;
  }
  //
  const res = await handleCheckInventory();
  if (!res) {
    const dataSettingOrg = data.value as any;
    const arrRoles = dataSettingOrg?.find(
      (org: any) => org?.storeId === orgId.value
    );
    if (arrRoles?.rolesExportInvoice?.length) {
      const arrRolesDefault = ["SALE_ADMIN", "SALE_MANAGER"];
      const isRoleSaleAdmin = auth.value?.user?.roles?.filter((role: any) =>
        arrRolesDefault?.includes(role)
      );
      if (!isRoleSaleAdmin?.length) {
        isAlertWareHouse.value = true;
        toogleConfirmExportExportWareHouse();
        return;
      }
    }
  }
  const dataVatRequest = {
    sourceId: dataInvoiceDraft.value?.invoiceId,
    sourceType: "invoice",
    signType: "MTT",
    includeBuyerTaxCode: selected.value === "COMPANY" ? true : false,
    buyerName: buyerName.value,
    buyerTaxCode: buyerTaxCode.value,
    buyerCompany: buyerCompany.value,
    buyerAddress: buyerAddress.value,
    sendMail: isSendMail.value,
    receiverName: isSendMail.value ? receiverName.value : "",
    receiverEmail: isSendMail.value ? receiverEmail.value : "",
  };
  if (selected.value === "COMPANY") {
    if (!buyerTaxCode.value) {
      useNuxtApp().$toast.warning("Vui lòng nhập mã số thuế");
      toogleConfirmExportExportWareHouse();
      return;
    }
    if (!selectTaxCode.value) {
      const response = await createVatInfo(
        buyerName.value,
        buyerTaxCode.value,
        receiverEmail.value,
        props.order?.order?.ownerPartyId,
        buyerAddress.value,
        auth.value?.user?.id
      );
    }
  }
  try {
    isLoading.value = true;
    const response = await requestPublishVatInvoice(
      dataVatRequest,
      auth.value?.user?.id
    );
    if (response.code === 1) {
      props.order.order.customAttribute.exportVatInvoiceStatus =
        "INVOICE_PUBLISHED";
      await handleGetInvoiceOfOrder();
      await orderStore.handleGetInvoiceOfOrder();
      toogleConfirmExportExportWareHouse();
    }
  } catch (error) {
    throw error;
  } finally {
    isLoading.value = false;
  }
};

const urlInvoice = ref();
const isLoading = ref(false);
const dataItemInvoice = ref([]);
const handleGetUrl = async (dataVatRequest: VatInvoiceRequestDTO) => {
  isLoading.value = true;
  try {
    const response = await requestUnpublishVatInvoice(
      dataVatRequest,
      auth.value?.user?.id
    );
    if (response?.previewLink) {
      dataInvoiceDraft.value = response;
      urlInvoice.value = response?.previewLink;
      const res = await getInvoiceItemOfInvoie(
        dataInvoiceDraft.value?.invoiceId
      );
      dataItemInvoice.value = res;
    }
  } catch (error) {
    throw error;
  } finally {
    isLoading.value = false;
  }
};
const handleUpdateItem = async () => {};
const dataVatCustomer = ref<any>([]);
const selectTaxCode = ref();
const handleGetVat = async (ownerPartyId: string) => {
  try {
    const response = await getVatInfoByOwnerPartyId(ownerPartyId);
    dataVatCustomer.value = response;
    if (dataVatCustomer.value.length) {
      selectTaxCode.value = dataVatCustomer.value[0];
      //
      buyerName.value = selectTaxCode.value?.company;
      buyerAddress.value = selectTaxCode.value?.address;
      if (selected.value === "COMPANY") {
        buyerCompany.value = selectTaxCode.value?.company;
        buyerTaxCode.value = selectTaxCode.value?.taxCode;
        receiverName.value = selectTaxCode.value?.company;
        receiverEmail.value = selectTaxCode.value?.invoiceReceiveEmail1;
      }
    }
    return response;
  } catch (error) {
    throw error;
  }
};
const dataListInvoice = ref<any>([]);
const selectedInvoice = ref();
const handleGetInvoiceOfOrder = async () => {
  try {
    const response = await getInvoicesOfOrder(props.order.id);
    dataListInvoice.value = response;
    if (dataListInvoice.value?.length >= 0) {
      handleSetInvoice(dataListInvoice.value[0]);
    }
    return response;
  } catch (error) {
    throw error;
  }
};
const handleGetInvoiceOfOrderDraft = async () => {
  try {
    const response = await getInvoicesOfOrder(props.order.id);
    const dataVatRequest = {
      sourceId: response?.length > 0 ? response[0]?.id : props.order.id,
      sourceType: response?.length > 0 ? "invoice" : "order",
      signType: "MTT",
      includeBuyerTaxCode: selected.value === "COMPANY" ? true : false,
      buyerName: buyerName.value,
      buyerTaxCode: selected.value === "COMPANY" ? buyerTaxCode.value : "",
      buyerCompany: selected.value === "COMPANY" ? buyerCompany.value : "",
      buyerAddress: buyerAddress.value,
      sendMail: isSendMail.value,
      receiverName: isSendMail.value ? receiverName.value : "",
      receiverEmail: isSendMail.value ? receiverEmail.value : "",
    };
    await handleGetUrl(dataVatRequest);
    return response;
  } catch (error) {
    throw error;
  }
};
const urlLinkOfficial = ref();

const handleGetInvoice = async () => {
  isLoading.value = true;
  try {
    const response = await viewPublishedInvoice(selectedInvoice.value);
    dataInvoiceDraft.value = response;
    urlInvoice.value = response?.previewLink;
    urlLinkOfficial.value = response?.lookupLink;
  } catch (error) {
    throw error;
  } finally {
    isLoading.value = false;
  }
};
const numberOfinvoice = ref();
const handleSetInvoice = async (invoice: any) => {
  numberOfinvoice.value = invoice?.attributes?.find(
    (item: any) => item?.name === "INV_NO"
  );

  selectedInvoice.value = invoice?.id;
  isLoading.value = true;
  try {
    const response = await viewPublishedInvoice(selectedInvoice.value);
    urlInvoice.value = response?.previewLink;
    urlLinkOfficial.value = response?.lookupLink;
  } catch (error) {
    throw error;
  } finally {
    isLoading.value = false;
  }
};
const { getUnitInvoice } = useProduct();
onMounted(async () => {
  selected.value = tabs.value[0].value;

  const order = props.order?.order;
  const customAttr = order?.customAttribute;
  buyerName.value = order?.ownerName;

  const exportStatus = customAttr?.exportVatInvoiceStatus;

  if (exportStatus === "INVOICE_DRAFT") {
    await handleGetInvoiceOfOrderDraft();
    return;
  }

  if (exportStatus === "INVOICE_PUBLISHED") {
    await handleGetInvoiceOfOrder();
    return;
  }

  const includeBuyerTaxCode = selected.value === "COMPANY";

  const dataVatRequest = {
    sourceId: props.order?.id,
    sourceType: "order",
    signType: "MTT",
    includeBuyerTaxCode,

    buyerName: order?.ownerName,
    buyerTaxCode: "",
    buyerCompany: "",
    buyerAddress: "",
    sendMail: false,
    receiverName: "",
    receiverEmail: "",
  };

  await Promise.allSettled([
    handleGetVat(order?.ownerPartyId),
    handleGetUrl(dataVatRequest),
  ]);

  await orderStore.updateOrder(props.order?.id);
});

const buyerName = ref<string>("");
const buyerTaxCode = ref<string>("");
const buyerCompany = ref<string>("");
const buyerAddress = ref<string>("");
const receiverName = ref<string>("");
const receiverEmail = ref<string>("");
const tabs = ref([
  { label: "Khách lẻ", value: "CUSTOMER" },
  { label: "Công ty", value: "COMPANY" },
]);
const handleSetTab = (name: string) => {
  selected.value = name;
};
const handleChangeTaxCode = () => {
  buyerName.value = selectTaxCode.value?.company;
  buyerAddress.value = selectTaxCode.value?.address;
  buyerCompany.value = selectTaxCode.value?.company;
  buyerTaxCode.value = selectTaxCode.value?.taxCode;
  receiverName.value = selectTaxCode.value?.company;
  receiverEmail.value = selectTaxCode.value?.invoiceReceiveEmail1;
};
const isConfirmExportInvoice = ref(false);
const toogleConfirmExportExportWareHouse = () => {
  isConfirmExportInvoice.value = !isConfirmExportInvoice.value;
};
const handleCopy = () => {
  console.log("urlLinkOfficial", urlLinkOfficial.value);
  navigator.clipboard.writeText(urlLinkOfficial.value);
  useNuxtApp().$toast.success("Đã lưu vào bộ nhớ tạm");
};
const dataChangeProduct = ref<any>([]);
const handleUpdateInvoice = async (data: any) => {
  if (!dataInvoiceDraft.value.invoiceId) {
    useNuxtApp().$toast.warning("Vui lòng tạo hóa đơn nháp");
    return;
  }
  if (dataChangeProduct.value.length >= 0) {
    const index = dataChangeProduct.value.findIndex(
      (item: any) => item.id === data.id
    );
    if (index !== -1) {
      dataChangeProduct.value[index] = data;
    } else {
      dataChangeProduct.value.push(data);
    }
  } else {
    dataChangeProduct.value.push(data);
  }
  console.log("dataChangeProduct", dataChangeProduct.value);
  await updateInvoiceItem(
    dataInvoiceDraft.value.invoiceId,
    dataChangeProduct.value[0],
    auth.value?.user?.id
  );
};
//
const isOpenEditItemInvoice = ref(false);
const toogleOpenEditPopup = () => {
  isOpenEditItemInvoice.value = !isOpenEditItemInvoice.value;
};
const handGetNewInvoiceDraft = async () => {
  if (buyerTaxCode.value) {
    if (!buyerCompany.value) {
      useNuxtApp().$toast.warning("Vui lòng nhập tên đơn vị");
      return;
    }
  }
  if (isSendMail.value) {
    if (!receiverName.value) {
      useNuxtApp().$toast.warning("Vui lòng nhập tên người nhận");
      return;
    }
    if (!receiverEmail.value) {
      useNuxtApp().$toast.warning("Vui lòng nhập email người nhận");
      return;
    }
  }
  if (selected.value === "COMPANY") {
    if (!buyerTaxCode.value) {
      useNuxtApp().$toast.warning("Vui lòng nhập mã số thuế");
      return;
    }
  }
  const dataVatRequest = {
    sourceId: dataInvoiceDraft.value?.invoiceId,
    sourceType: "invoice",
    signType: "MTT",
    includeBuyerTaxCode: selected.value === "COMPANY" ? true : false,
    buyerName: buyerName.value,
    buyerTaxCode: selected.value === "COMPANY" ? buyerTaxCode.value : "",
    buyerCompany: selected.value === "COMPANY" ? buyerCompany.value : "",
    buyerAddress: buyerAddress.value,
    sendMail: isSendMail.value,
    receiverName: isSendMail.value ? receiverName.value : "",
    receiverEmail: isSendMail.value ? receiverEmail.value : "",
  };
  await handleGetUrl(dataVatRequest);

  toogleOpenEditPopup();
};
</script>
