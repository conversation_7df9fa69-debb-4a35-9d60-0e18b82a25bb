export default defineNuxtRouteMiddleware(async (to: any) => {
  const { setToken } = useAuth();
  const token = useCookie("token") as Ref<string>;
  const { checkToken } = useAuth();

  const { orgId } = useTabContext();
  if (!token.value) {
    const query: Record<string, string> = {};

    if (to.path !== "/" && to.path !== "/login") {
      query.path = to.path;
    }

    if (to.query.orgId) query.orgId = String(to.query.orgId);
    if (to.query.storeId) query.storeId = String(to.query.storeId);
    if (to.query.orderId) query.orderId = String(to.query.orderId);
    if (to.query.customerId) query.customerId = String(to.query.customerId);
    return navigateTo({ path: "/login", query });
  } else {
    await setToken(token.value);
    try {
      await checkToken(orgId.value, token.value);
    } catch (error) {
      return navigateTo("/login");
      // Handle errors, particularly 401 Unauthorized
      // console.log("error", error);
      // if (typeof error === "object" && error !== null && "response" in error) {
      //   const err = error as { response: { status: number } };
      //   if (err.response.status === 422) {
      //     return navigateTo("/login");
      //   } else {
      //     throw error;
      //   }
      // } else {
      //   throw error;
      // }
    }
  }
});
